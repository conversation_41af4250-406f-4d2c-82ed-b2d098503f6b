// Debug script to check database state
// Run this in the app console to debug database issues

const debugDatabase = async () => {
  try {
    const { getDatabase } = await import('./src/services/database/init');
    const db = getDatabase();
    
    console.log('=== DATABASE DEBUG ===');
    
    // Check all sessions
    const sessions = await db.getAllAsync(
      `SELECT id, type, title, duration_seconds, is_completed, volumes, created_at, completed_at
       FROM sessions
       ORDER BY created_at DESC
       LIMIT 10`
    );
    
    console.log('Recent sessions:', sessions.map(s => ({
      id: s.id.substring(0, 8),
      type: s.type,
      duration: s.duration_seconds,
      completed: s.is_completed,
      volumes: s.volumes,
      created: s.created_at,
      completedAt: s.completed_at
    })));
    
    // Check answer records
    const answers = await db.getAllAsync(
      `SELECT question_id, volume, is_correct, mode, session_id, created_at
       FROM answer_records
       ORDER BY created_at DESC
       LIMIT 20`
    );
    
    console.log('Recent answer records:', answers.map(a => ({
      questionId: a.question_id,
      volume: a.volume,
      isCorrect: a.is_correct,
      mode: a.mode,
      sessionId: a.session_id.substring(0, 8),
      created: a.created_at
    })));
    
    // Check volume progress
    const progress = await db.getAllAsync(
      `SELECT * FROM volume_progress ORDER BY volume`
    );
    
    console.log('Volume progress:', progress);
    
    // Check question stats
    const stats = await db.getAllAsync(
      `SELECT question_id, volume, total_attempts, correct_attempts, wrong_attempts
       FROM question_stats
       WHERE total_attempts > 0
       ORDER BY volume, question_id
       LIMIT 20`
    );
    
    console.log('Question stats (first 20):', stats);
    
    console.log('=== END DEBUG ===');
    
  } catch (error) {
    console.error('Debug failed:', error);
  }
};

// Export for use in console
window.debugDatabase = debugDatabase;

console.log('Debug function loaded. Run debugDatabase() in console to check database state.');
