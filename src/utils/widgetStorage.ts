import { ExtensionStorage } from '@bacons/apple-targets';

const storage = new ExtensionStorage('group.com.248labs.macaudrivesaver');

export interface WidgetData {
  totalAvailableQuestions: number;
  correctAnswers: number;
  totalAnswered: number;
  examDate: string;
}

export const updateWidgetData = (data: WidgetData) => {
  try {
    // Calculate derived values for the widget
    const accuracy = data.totalAnswered > 0 ? (data.correctAnswers / data.totalAnswered) * 100 : 0;

    // Use the same calculation as dashboard: mastered questions / total questions seen
    // But for widget display, we'll show progress against all 647 questions
    const totalAllQuestions = 647; // Total questions across all volumes (176+66+150+139+116)
    const studyProgress = Math.round((data.correctAnswers / totalAllQuestions) * 100);

    // Create widget data object
    const widgetData = {
      examDate: data.examDate,
      daysRemaining: null, // Can be calculated if exam date is set
      studyProgress: studyProgress,
      masteredQuestions: data.correctAnswers,
      totalQuestions: totalAllQuestions, // Use total available questions (647)
      accuracy: accuracy
    };

    // Store as JSON string under 'widgetData' key (as expected by the widget)
    const jsonString = JSON.stringify(widgetData);
    storage.set('widgetData', jsonString);
    console.log('Widget data updated successfully:', widgetData);
    console.log('Widget data JSON string:', jsonString);

    // Verify the data was stored
    const storedData = storage.get('widgetData');
    console.log('Widget data verification - stored:', storedData);
  } catch (error) {
    console.error('Failed to update widget data:', error);
  }
};

export const getWidgetData = (): WidgetData => {
  try {
    const widgetDataString = storage.get('widgetData') as string;
    if (widgetDataString) {
      const widgetData = JSON.parse(widgetDataString);
      return {
        totalAvailableQuestions: 1000, // This would need to be stored separately if needed
        correctAnswers: widgetData.masteredQuestions || 0,
        totalAnswered: widgetData.totalQuestions || 0,
        examDate: widgetData.examDate || '未設定',
      };
    }

    // Return default values if no data found
    return {
      totalAvailableQuestions: 1000,
      correctAnswers: 0,
      totalAnswered: 0,
      examDate: '未設定',
    };
  } catch (error) {
    console.error('Failed to get widget data:', error);
    return {
      totalAvailableQuestions: 1000,
      correctAnswers: 0,
      totalAnswered: 0,
      examDate: '未設定',
    };
  }
};
