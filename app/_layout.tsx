import { COLORS } from '@/src/utils/constants';
import { Ionicons } from '@expo/vector-icons';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { router, Stack } from 'expo-router';
import { useEffect } from 'react';
import { TouchableOpacity, useColorScheme } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import 'react-native-reanimated';
import '../global.css';
import { initializeDatabase } from '../src/services/database';

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  useEffect(() => {
    // Initialize database when app starts
    initializeDatabase().catch(error => {
      console.error('Failed to initialize database on app start:', error);
    });
  }, []);

  if (!loaded) {
    return null;
  }

  return (
    <GestureHandlerRootView className="flex-1">
      <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
        <BottomSheetModalProvider>
          <Stack screenOptions={{
            headerTransparent: false,
            headerBlurEffect: undefined,
            headerLargeTitle: false,
            headerTintColor: COLORS.TEXT,
            headerStyle: {
              backgroundColor: COLORS.BACKGROUND,
            },
            headerShadowVisible: false,
          }}>
            <Stack.Screen name="index" options={{
              title: '首頁',
              headerShown: false
            }} />
            {/* 練習 */}
            <Stack.Screen
              name="practice/options"
              options={{
                title: '練習模式',
                headerShown: true,
                headerLeft: () => (
                  <TouchableOpacity onPress={() => router.back()} className="pr-2">
                    <Ionicons name="chevron-back" size={28} color="black" />
                  </TouchableOpacity>
                ),
              }}
            />
            <Stack.Screen
              name="practice/volume/[volume]"
              options={{
                title: '冊別練習',
                headerShown: true,
                headerLeft: () => (
                  <TouchableOpacity onPress={() => router.back()} className="pr-2">
                    <Ionicons name="chevron-back" size={28} color="black" />
                  </TouchableOpacity>
                ),
              }}
            />
            <Stack.Screen
              name="practice/volume/[volume]/guidebook"
              options={{
                title: '導覽書',
                headerShown: true,
                headerLeft: () => (
                  <TouchableOpacity onPress={() => router.back()} className="pr-2">
                    <Ionicons name="chevron-back" size={28} color="black" />
                  </TouchableOpacity>
                ),
              }}
            />
            <Stack.Screen
              name="practice/session"
              options={{
                title: '練習模式',
                headerShown: true,
                gestureEnabled: false,
              }}
            />
            {/* 錯題回顧 */}
            <Stack.Screen
              name="review/index"
              options={{
                title: '錯題複習',
                headerShown: true,
                headerLeft: () => (
                  <TouchableOpacity onPress={() => router.back()} className="pr-2">
                    <Ionicons name="chevron-back" size={28} color="black" />
                  </TouchableOpacity>
                ),
              }}
            />
            <Stack.Screen
              name="review/session"
              options={{
                title: '複習模式',
                headerShown: true,
                gestureEnabled: false,
              }}
            />
            {/* 模擬考試 */}
            <Stack.Screen
              name="exam/index"
              options={{
                title: '模擬考試',
                headerShown: true,
                headerLeft: () => (
                  <TouchableOpacity onPress={() => router.back()} className="pr-2">
                    <Ionicons name="chevron-back" size={28} color="black" />
                  </TouchableOpacity>
                ),
              }}
            />
            <Stack.Screen
              name="exam/session"
              options={{
                title: '模擬考試',
                headerShown: true,
                gestureEnabled: false,
              }}
            />
            <Stack.Screen
              name="exam/result"
              options={{
                title: '考試結果',
                headerShown: true,
                gestureEnabled: false,
              }}
            />
            <Stack.Screen name="+not-found" />
          </Stack>
        </BottomSheetModalProvider>
      </ThemeProvider>
    </GestureHandlerRootView>
  );
}
