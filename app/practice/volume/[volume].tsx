import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams, useNavigation } from 'expo-router';
import React, { useEffect, useLayoutEffect, useState } from 'react';
import { Alert, Platform, ScrollView, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Button, Card, LoadingSpinner } from '../../../src/components/common';
import { useStatistics } from '../../../src/hooks/useStatistics';
import { getDatabase } from '../../../src/services/database/init';
import { QuestionLoader } from '../../../src/services/questions/loader';
import { QuestionManager } from '../../../src/services/questions/manager';
import { usePracticeStore } from '../../../src/store/usePracticeStore';
import { ProcessedQuestion } from '../../../src/types/question';
import { PracticeConfig } from '../../../src/types/session';
import { COLORS, VOLUMES } from '../../../src/utils/constants';

interface VolumeProgressBarProps {
  correct: number;
  wrong: number;
  unseen: number;
  total: number;
}

function VolumeProgressBar({ correct, wrong, unseen, total }: VolumeProgressBarProps) {
  const correctPercentage = total > 0 ? (correct / total) * 100 : 0;
  const wrongPercentage = total > 0 ? (wrong / total) * 100 : 0;
  const unseenPercentage = total > 0 ? (unseen / total) * 100 : 0;

  return (
    <View className="w-full">
      <View className="h-3 bg-gray-200 rounded-full overflow-hidden flex-row shadow-inner">
        <View
          className="h-full rounded-l-full"
          style={{
            width: `${correctPercentage}%`,
            backgroundColor: COLORS.SUCCESS
          }}
        />
        <View
          className="h-full"
          style={{
            width: `${wrongPercentage}%`,
            backgroundColor: COLORS.ERROR
          }}
        />
        <View
          className="h-full rounded-r-full"
          style={{
            width: `${unseenPercentage}%`,
            backgroundColor: COLORS.BORDER
          }}
        />
      </View>

      {/* Progress Legend */}
      {/* <View className="flex-row justify-between mt-2">
        <View className="flex-row items-center">
          <View className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: COLORS.SUCCESS }} />
          <Text className="text-12 text-text-light">已掌握</Text>
        </View>
        <View className="flex-row items-center">
          <View className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: COLORS.ERROR }} />
          <Text className="text-12 text-text-light">錯題</Text>
        </View>
        <View className="flex-row items-center">
          <View className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: COLORS.BORDER }} />
          <Text className="text-12 text-text-light">未完成</Text>
        </View>
      </View> */}
    </View>
  );
}



// Function to calculate study time for a volume
async function calculateVolumeStudyTime(volumeNumber: number): Promise<number> {
  try {
    const db = getDatabase();



    const result = await db.getFirstAsync(
      `SELECT SUM(duration_seconds) as total_time
       FROM sessions
       WHERE type = 'practice'
       AND is_completed = 1
       AND volumes LIKE '%[${volumeNumber}]%'`,
      []
    ) as { total_time: number | null };



    return Math.floor((result?.total_time || 0) / 60); // Convert to minutes
  } catch (error) {
    console.error('Failed to calculate study time:', error);
    return 0;
  }
}

// Convert Arabic numerals to Chinese numerals for small numbers (1-20)
function toChineseNumber(num: number): string {
  const digits = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
  if (num <= 10) {
    const map: Record<number, string> = { 10: '十' };
    return map[num] || digits[num];
  }
  if (num < 20) {
    return `十${digits[num - 10]}`;
  }
  if (num % 10 === 0) {
    return `${digits[Math.floor(num / 10)]}十`;
  }
  return `${digits[Math.floor(num / 10)]}十${digits[num % 10]}`;
}

export default function VolumeEntryScreen() {
  const { volume } = useLocalSearchParams<{ volume: string }>();
  const volumeNumber = parseInt(volume || '1', 10);
  const { volumeDisplayStats } = useStatistics();
  const { startSession } = usePracticeStore();
  const [loading, setLoading] = useState(false);
  const [questions, setQuestions] = useState<ProcessedQuestion[]>([]);
  const [loadingQuestions, setLoadingQuestions] = useState(true);
  const [studyTime, setStudyTime] = useState(0);
  const navigation = useNavigation();

  // Prepare title parts early to avoid hook order issues
  const volumeNameForTitle = VOLUMES.NAMES[volumeNumber as keyof typeof VOLUMES.NAMES] || '未知冊別';
  const chineseVolumeNumber = toChineseNumber(volumeNumber);

  // Dynamic header configuration (must be before any conditional return)
  useLayoutEffect(() => {
    navigation.setOptions({
      title: `第${chineseVolumeNumber}冊 - ${volumeNameForTitle}`,
    });
  }, [navigation, chineseVolumeNumber, volumeNameForTitle]);

  // Find volume stats
  const volumeStats = volumeDisplayStats?.find(stats => stats.volume === volumeNumber);

  useEffect(() => {
    loadVolumeQuestions();
    loadStudyTime();
  }, [volumeNumber]);

  const loadVolumeQuestions = async () => {
    try {
      setLoadingQuestions(true);
      const volumeQuestions = await QuestionLoader.loadVolume(volumeNumber);
      setQuestions(volumeQuestions);
    } catch (error) {
      console.error('Failed to load volume questions:', error);
      Alert.alert('錯誤', '無法載入題目，請稍後再試。');
    } finally {
      setLoadingQuestions(false);
    }
  };

  const loadStudyTime = async () => {
    const time = await calculateVolumeStudyTime(volumeNumber);
    setStudyTime(time);
  };

  const handleStartPractice = async () => {
    if (!volumeStats) {
      Alert.alert('錯誤', '無法載入冊別統計數據。');
      return;
    }

    setLoading(true);

    try {
      const hasWrongQuestions = volumeStats.wrong > 0;
      const hasUnseenQuestions = volumeStats.unseen > 0;

      const config: PracticeConfig = {
        volumes: [volumeNumber],
        chapter: null,
        mode: 'random',
        includeWrongQuestions: hasWrongQuestions,
        includeBookmarked: false,
        includeUnseen: hasUnseenQuestions,
      };

      const practiceQuestions = await QuestionManager.generatePracticeQuestions(config);

      if (practiceQuestions.length === 0) {
        Alert.alert('沒有題目', '此冊別內沒有找到題目，請稍後再試。');
        return;
      }

      const allMastered = volumeStats.correct >= volumeStats.total;

      if (allMastered) {
        Alert.alert(
          '恭喜完成！',
          '您已掌握此冊的所有題目！現在將開始複習模式，您可以重新練習所有題目來保持熟練度。',
          [{
            text: '開始複習', onPress: async () => {
              await startSession(config, practiceQuestions);
              router.push('/practice/session');
            }
          }]
        );
        return;
      }

      await startSession(config, practiceQuestions);
      router.push('/practice/session');
    } catch (error) {
      console.error('Failed to start volume practice:', error);
      Alert.alert('錯誤', '無法載入練習題目，請稍後再試。');
    } finally {
      setLoading(false);
    }
  };

  if (!volumeStats || loadingQuestions) {
    return (
      <SafeAreaView className="flex-1 bg-background items-center justify-center">
        <LoadingSpinner color={COLORS.THEME} message="正在載入冊別資料..." />
      </SafeAreaView>
    );
  }

  const accuracy = volumeStats.total > 0 ? ((volumeStats.correct / volumeStats.total) * 100) : 0;

  return (
    <SafeAreaView className="flex-1 bg-background" edges={['bottom', 'left', 'right']}>
      <ScrollView className="flex-1 p-4" showsVerticalScrollIndicator={false}>
        {/* Dashboard */}
        <Card className="mb-0 shadow-sm">
          <View className="p-2">
            {/* Progress Bar Section */}
            <View className="mb-4">
              <View className="flex-row items-center mb-3">
                <View className="w-1 h-5 bg-theme rounded-full mr-3" />
                <Text className="text-16 font-semibold text-text">學習進度</Text>
              </View>
              <VolumeProgressBar
                correct={volumeStats.correct}
                wrong={volumeStats.wrong}
                unseen={volumeStats.unseen}
                total={volumeStats.total}
              />
            </View>

            {/* Stats Cards Row 1 */}
            <View className="flex-row justify-between mb-0">
              <View className="flex-1 items-center bg-success/10 rounded-xl p-3 mr-2">
                <Text className="text-24 font-bold text-success">{volumeStats.correct}</Text>
                <Text className="text-12 text-text-light font-medium">已掌握</Text>
              </View>
              <View className="flex-1 items-center bg-error/10 rounded-xl p-3 mx-1">
                <Text className="text-24 font-bold text-error">{volumeStats.wrong}</Text>
                <Text className="text-12 text-text-light font-medium">錯題</Text>
              </View>
              <View className="flex-1 items-center bg-gray-100 rounded-xl p-3 ml-2">
                <Text className="text-24 font-bold text-text-light">{volumeStats.unseen}</Text>
                <Text className="text-12 text-text-light font-medium">未完成</Text>
              </View>
            </View>

            {/* Stats Row 2 */}
            <View className="bg-card-light rounded-xl p-4 pb-0">
              <View className="flex-row justify-between">
                <View className="flex-1">
                  <Text className="text-12 text-text-light mb-1">完成情況</Text>
                  <Text className="text-16 font-semibold text-text">
                    {volumeStats.correct + volumeStats.wrong}/{volumeStats.total}
                  </Text>
                </View>
                <View className="flex-1 items-center">
                  <Text className="text-12 text-text-light mb-1">完成進度</Text>
                  <Text className="text-16 font-semibold text-text">
                    {accuracy.toFixed(1)}%
                  </Text>
                </View>
                <View className="flex-1 items-end">
                  <Text className="text-12 text-text-light mb-1">學習時間</Text>
                  <Text className="text-16 font-semibold text-text">
                    {studyTime || 0}分鐘
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </Card>

        {/* Guidebook Button */}
        {questions.length > 0 && (
          <Card className="mt-4 shadow-sm" onPress={() => router.push(`/practice/volume/${volumeNumber}/guidebook`)}>
            <View className="p-2">
              <View className="flex-row items-center justify-between">
                <View className="flex-1">
                  <View className="flex-row items-center mb-2">
                    <View className="w-12 h-12 bg-theme/10 rounded-full items-center justify-center mr-3">
                      <Ionicons name="book" size={24} color={COLORS.THEME_TEXT} />
                    </View>
                    <View className="flex-1">
                      <Text className="text-16 font-semibold text-text">導覽書</Text>
                      <Text className="text-12 text-text-light">
                        第{chineseVolumeNumber}冊 - {VOLUMES.NAMES[volumeNumber as keyof typeof VOLUMES.NAMES]} ({questions.length}題)
                      </Text>
                    </View>
                  </View>
                  <View className="bg-theme/5 rounded-xl p-3">
                    <Text className="text-14 text-text-light">
                      瀏覽所有題目和答案，快速掌握重點內容
                    </Text>
                  </View>
                </View>
                <View className="ml-4">
                  <Ionicons name="chevron-forward" size={20} color={COLORS.TEXT_LIGHT} />
                </View>
              </View>
            </View>
          </Card>
        )}
      </ScrollView>

      {/* Bottom Action Button */}
      <View className={`bg-background border-t border-border p-4  ${Platform.OS === 'ios' ? '' : 'mb-5'}`}>
        <View className="flex-row justify-center gap-3">
          <Button
            title="開始練習"
            onPress={handleStartPractice}
            style={{ flex: 1, backgroundColor: COLORS.THEME }}
            size="medium"
            loading={loading}
          />
        </View>
      </View>
    </SafeAreaView>
  );
}
