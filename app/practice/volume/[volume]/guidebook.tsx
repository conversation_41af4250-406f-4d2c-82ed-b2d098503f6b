import { Ionicons } from '@expo/vector-icons';
import { useLocalSearchParams, useNavigation } from 'expo-router';
import React, { useEffect, useLayoutEffect, useState } from 'react';
import { Alert, Image, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Card, LoadingSpinner } from '../../../../src/components/common';
import { getDatabase } from '../../../../src/services/database/init';
import { IMAGE_MAP, ImageKey } from '../../../../src/services/questions/imageMap';
import { QuestionLoader } from '../../../../src/services/questions/loader';
import { ProcessedQuestion } from '../../../../src/types/question';
import { COLORS, VOLUMES } from '../../../../src/utils/constants';

// Convert Arabic numerals to Chinese numerals for small numbers (1-20)
function toChineseNumber(num: number): string {
  const digits = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
  if (num <= 10) {
    const map: Record<number, string> = { 10: '十' };
    return map[num] || digits[num];
  }
  if (num < 20) {
    return `十${digits[num - 10]}`;
  }
  if (num % 10 === 0) {
    return `${digits[Math.floor(num / 10)]}十`;
  }
  return `${digits[Math.floor(num / 10)]}十${digits[num % 10]}`;
}

export default function GuidebookScreen() {
  const { volume } = useLocalSearchParams<{ volume: string }>();
  const volumeNumber = parseInt(volume || '1', 10);
  const [questions, setQuestions] = useState<ProcessedQuestion[]>([]);
  const [loadingQuestions, setLoadingQuestions] = useState(true);
  const [expandedQuestions, setExpandedQuestions] = useState<Set<number>>(new Set());
  const [questionStats, setQuestionStats] = useState<Record<string, { isCorrect: boolean; hasAnswered: boolean }>>({});
  const navigation = useNavigation();

  const hasImages = volumeNumber === 1 || volumeNumber === 2;
  const chineseVolumeNumber = toChineseNumber(volumeNumber);
  const volumeNameForTitle = VOLUMES.NAMES[volumeNumber as keyof typeof VOLUMES.NAMES] || '未知冊別';

  // Dynamic header configuration
  useLayoutEffect(() => {
    navigation.setOptions({
      title: `導覽書 - 第${chineseVolumeNumber}冊`,
    });
  }, [navigation, chineseVolumeNumber]);

  useEffect(() => {
    loadVolumeQuestions();
  }, [volumeNumber]);

  // Load question statistics
  useEffect(() => {
    if (questions.length === 0) return;

    const loadQuestionStats = async () => {
      try {
        const db = getDatabase();
        const stats: Record<string, { isCorrect: boolean; hasAnswered: boolean }> = {};

        for (const question of questions) {
          // Get the most recent answer record for this question AND volume
          const result = await db.getFirstAsync(
            `SELECT is_correct
             FROM answer_records
             WHERE question_id = ? AND volume = ? AND mode = 'practice'
             ORDER BY created_at DESC
             LIMIT 1`,
            [question.id, volumeNumber]
          ) as { is_correct: number | null } | null;

          if (result !== null) {
            // Question has been answered
            stats[question.id.toString()] = {
              isCorrect: result.is_correct === 1,
              hasAnswered: true
            };
          } else {
            // Question has not been answered
            stats[question.id.toString()] = {
              isCorrect: false,
              hasAnswered: false
            };
          }
        }

        setQuestionStats(stats);
      } catch (error) {
        console.error('Failed to load question stats:', error);
      }
    };

    loadQuestionStats();
  }, [questions, volumeNumber]);

  const loadVolumeQuestions = async () => {
    try {
      setLoadingQuestions(true);
      const volumeQuestions = await QuestionLoader.loadVolume(volumeNumber);
      setQuestions(volumeQuestions);
    } catch (error) {
      console.error('Failed to load volume questions:', error);
      Alert.alert('錯誤', '無法載入題目，請稍後再試。');
    } finally {
      setLoadingQuestions(false);
    }
  };

  // Get status color for a question
  const getStatusColor = (questionId: number) => {
    const stat = questionStats[questionId.toString()];
    if (!stat?.hasAnswered) return 'bg-gray-300'; // Not answered
    return stat.isCorrect ? 'bg-success' : 'bg-error'; // Correct or wrong
  };

  const toggleQuestion = (questionIndex: number) => {
    setExpandedQuestions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(questionIndex)) {
        newSet.delete(questionIndex);
      } else {
        newSet.add(questionIndex);
      }
      return newSet;
    });
  };

  if (loadingQuestions) {
    return (
      <SafeAreaView className="flex-1 bg-background items-center justify-center">
        <LoadingSpinner color={COLORS.THEME} message="正在載入導覽書..." />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-background" edges={['bottom', 'left', 'right']}>
      <ScrollView className="flex-1 p-4" showsVerticalScrollIndicator={true}>
        {/* Header Info */}
        <Card className="mb-4 shadow-sm">
          <View className="p-2">
            <View className="flex-row items-center mb-2">
              <View className="w-12 h-12 bg-theme/10 rounded-full items-center justify-center mr-3">
                <Ionicons name="book" size={24} color={COLORS.THEME_TEXT} />
              </View>
              <View className="flex-1">
                <Text className="text-18 font-semibold text-text">導覽書</Text>
                <Text className="text-14 text-text-light">
                  第{chineseVolumeNumber}冊 - {volumeNameForTitle} ({questions.length}題)
                </Text>
              </View>
            </View>
            <View className="bg-theme/5 rounded-xl p-3">
              <Text className="text-14 text-text-light">
                瀏覽所有題目和答案，快速掌握重點內容
              </Text>
            </View>
          </View>
        </Card>

        {/* Questions List */}
        <Card className="shadow-sm">
          <View className="p-2">
            <ScrollView className="max-h-96" showsVerticalScrollIndicator={true}>
              {questions.map((question, index) => {
                const isExpanded = expandedQuestions.has(index);
                const correctOption = question.options.find(opt => opt.isCorrect);

                return (
                  <View key={question.id} className="mb-3">
                    <TouchableOpacity
                      onPress={() => toggleQuestion(index)}
                      className="bg-card-light rounded-lg p-4 border border-border"
                    >
                {hasImages && question.image ? (
                  // Layout for volumes with images (1 & 2): Image left, content right
                  <View className="flex-row">
                    {/* Left side: Image */}
                    <View className="w-32 mr-4 bg-gray-100 rounded-lg overflow-hidden" style={{ height: 100 }}>
                      <Image
                        source={(IMAGE_MAP as Record<string, number>)[question.image as ImageKey] ?? { uri: question.image }}
                        style={{ height: 100, width: '100%' }}
                        resizeMode="contain"
                      />
                    </View>

                    {/* Right side: Question content */}
                    <View className="flex-1">
                      {/* Header with status and expand icon */}
                      <View className="flex-row justify-between items-start mb-1">
                        <View className="flex-row items-start flex-1 mr-2">
                          <View className={`w-3 h-3 rounded-full mr-2 mt-0.5 ${getStatusColor(question.id)}`} />
                          <Text className="text-14 text-text flex-1" numberOfLines={isExpanded ? undefined : 2}>
                            {index + 1}. {question.question}
                          </Text>
                        </View>
                        <View className="">
                          <Ionicons
                            name={isExpanded ? "chevron-up" : "chevron-down"}
                            size={20}
                            color={COLORS.TEXT_LIGHT}
                          />
                        </View>
                      </View>

                      {/* Answer preview when collapsed */}
                      {!isExpanded && correctOption && (
                        <Text className="text-12 text-success font-semibold mt-1">
                          答案：{correctOption.text}
                        </Text>
                      )}
                    </View>
                  </View>
                ) : (
                  // Layout for volumes without images (3, 4, 5): Standard layout
                  <>
                    {/* Header with status and expand icon */}
                    <View className="flex-row justify-between items-start mb-0">
                      <View className="flex-row items-start flex-1 mr-3">
                        <View className={`w-3 h-3 rounded-full mr-2 mt-0.5 ${getStatusColor(question.id)}`} />
                        <Text className="text-14 text-text flex-1" numberOfLines={isExpanded ? undefined : 2}>
                          {index + 1}. {question.question}
                        </Text>
                      </View>
                      <View className="">
                        <Ionicons
                          name={isExpanded ? "chevron-up" : "chevron-down"}
                          size={20}
                          color={COLORS.TEXT_LIGHT}
                        />
                      </View>
                    </View>

                    {/* Answer preview when collapsed */}
                    {!isExpanded && correctOption && (
                      <Text className="text-12 text-success font-semibold mt-2">
                        答案：{correctOption.text}
                      </Text>
                    )}
                  </>
                )}

                {/* Expanded Options */}
                {isExpanded && (
                  <View className="mt-4 pt-4 border-t border-border">
                    {question.options.map((option, optionIndex) => (
                      <View key={optionIndex} className="flex-row items-start mb-2">
                        <View className={`w-6 h-6 rounded-full mr-3 items-center justify-center ${
                          option.isCorrect ? 'bg-success' : 'bg-border'
                        }`}>
                          <Text className={`text-12 font-semibold ${
                            option.isCorrect ? 'text-white' : 'text-text-light'
                          }`}>
                            {['A', 'B', 'C', 'D'][optionIndex]}
                          </Text>
                        </View>
                        <Text className={`flex-1 text-14 ${
                          option.isCorrect ? 'text-success font-semibold' : 'text-text'
                        }`}>
                          {option.text}
                        </Text>
                      </View>
                    ))}
                  </View>
                )}
                    </TouchableOpacity>
                  </View>
                );
              })}
            </ScrollView>
          </View>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
}
